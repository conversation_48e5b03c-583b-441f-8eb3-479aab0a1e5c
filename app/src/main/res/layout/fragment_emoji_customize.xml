<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/grey">

    <!-- Banner Ad Container -->
    <FrameLayout
        android:id="@+id/bannerAdContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- Main Content ScrollView -->
    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:overScrollMode="never"
        app:layout_constraintBottom_toTopOf="@id/bannerAdContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <!-- Back Navigation -->
            <include
                android:id="@+id/backNavigation"
                layout="@layout/layout_back_navigation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp" />

            <!-- Header Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <TextView
                    android:id="@+id/customizeTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:layout_weight="1"
                    android:text="@string/customize_emoji_battery"
                    android:textColor="?attr/black"
                    android:textSize="19sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/customizeInfo"
                    android:layout_width="22sp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="5dp"
                    android:scaleType="fitEnd"
                    android:src="@drawable/ic_note"
                    android:visibility="visible" />
            </LinearLayout>

            <!-- Global Toggle Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:background="@drawable/rounded_background"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/enable_emoji_battery"
                        android:textColor="?attr/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="@string/enable_emoji_battery_description"
                        android:textColor="?attr/black"
                        android:textSize="14sp"
                        android:alpha="0.7" />
                </LinearLayout>

                <Switch
                    android:id="@+id/globalToggleSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp" />
            </LinearLayout>

            <!-- Live Preview Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/rounded_background"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="@string/live_preview"
                    android:textColor="?attr/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- Preview Container -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/previewContainer"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_120sdp"
                    android:background="@drawable/preview_background"
                    android:layout_marginBottom="12dp">

                    <!-- Preview Content will be dynamically added here -->
                    <RelativeLayout
                        android:id="@+id/previewContent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent">

                        <!-- Battery Preview -->
                        <ImageView
                            android:id="@+id/previewBattery"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:scaleType="centerInside"
                            android:layout_alignParentEnd="true"
                            android:layout_marginStart="@dimen/_120sdp"
                            android:src="@drawable/test_battery" />


                        <!-- Emoji Preview -->
                        <ImageView
                            android:id="@+id/previewEmoji"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:scaleType="centerInside"
                            android:layout_alignParentEnd="true"
                            android:layout_marginStart="@dimen/_120sdp"
                            android:src="@drawable/test_emoji" />

                        <!-- Percentage Preview -->
                        <TextView
                            android:id="@+id/previewPercentage"
                            android:layout_width="@dimen/_120sdp"
                            android:layout_height="match_parent"
                            android:text="50%"
                            android:textColor="?attr/black"
                            android:textSize="@dimen/_25ssp"
                            android:textStyle="bold"
                            android:gravity="center"/>
                    </RelativeLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- Preview Battery Level Slider -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/preview_level"
                        android:textColor="?attr/black"
                        android:textSize="14sp"
                        android:layout_marginEnd="12dp" />

                    <SeekBar
                        android:id="@+id/previewLevelSlider"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="100"
                        android:progress="50" />

                    <TextView
                        android:id="@+id/previewLevelText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="50%"
                        android:textColor="?attr/black"
                        android:textSize="14sp"
                        android:layout_marginStart="12dp"
                        android:minWidth="40dp"
                        android:gravity="center" />
                </LinearLayout>
            </LinearLayout>

            <!-- Style Selection Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/rounded_background"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="@string/battery_style"
                    android:textColor="?attr/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- Battery Styles RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/batteryStylesRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:layout_marginBottom="16dp"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="@string/emoji_character"
                    android:textColor="?attr/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- Emoji Styles RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/emojiStylesRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
            </LinearLayout>

            <!-- Customization Toggles Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/rounded_background"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="@string/display_options"
                    android:textColor="?attr/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- Show Emoji Toggle -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/show_emoji"
                        android:textColor="?attr/black"
                        android:textSize="14sp" />

                    <Switch
                        android:id="@+id/showEmojiSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />
                </LinearLayout>

                <!-- Show Percentage Toggle -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/show_battery_percentage"
                        android:textColor="?attr/black"
                        android:textSize="14sp" />

                    <Switch
                        android:id="@+id/showPercentageSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />
                </LinearLayout>
            </LinearLayout>

            <!-- Customization Sliders Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/rounded_background"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="@string/customization_options"
                    android:textColor="?attr/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- Percentage Font Size Slider -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/percentage_font_size"
                    android:textColor="?attr/black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="5dp"
                        android:textColor="?attr/black"
                        android:textSize="12sp"
                        android:layout_marginEnd="8dp" />

                    <SeekBar
                        android:id="@+id/fontSizeSlider"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="35"
                        android:progress="9" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="40dp"
                        android:textColor="?attr/black"
                        android:textSize="12sp"
                        android:layout_marginStart="8dp" />

                    <TextView
                        android:id="@+id/fontSizeValue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="14dp"
                        android:textColor="?attr/black"
                        android:textSize="12sp"
                        android:layout_marginStart="12dp"
                        android:minWidth="40dp"
                        android:gravity="center" />
                </LinearLayout>

                <!-- Emoji Size Scale Slider -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/emoji_size_scale"
                    android:textColor="?attr/black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0.5x"
                        android:textColor="?attr/black"
                        android:textSize="12sp"
                        android:layout_marginEnd="8dp" />

                    <SeekBar
                        android:id="@+id/emojiScaleSlider"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="15"
                        android:progress="5" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2.0x"
                        android:textColor="?attr/black"
                        android:textSize="12sp"
                        android:layout_marginStart="8dp" />

                    <TextView
                        android:id="@+id/emojiScaleValue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1.0x"
                        android:textColor="?attr/black"
                        android:textSize="12sp"
                        android:layout_marginStart="12dp"
                        android:minWidth="40dp"
                        android:gravity="center" />
                </LinearLayout>
            </LinearLayout>

            <!-- Loading State -->
            <FrameLayout
                android:id="@+id/loadingContainer"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginTop="16dp"
                android:visibility="gone">

                <ProgressBar
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="60dp"
                    android:text="@string/loading_customization"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
            </FrameLayout>

            <!-- Error State -->
            <LinearLayout
                android:id="@+id/errorContainer"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@drawable/ic_error"
                    android:alpha="0.6" />

                <TextView
                    android:id="@+id/errorMessage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:text="@string/error_loading_customization"
                    android:textColor="?attr/black"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/retryButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/retry" />
            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <!-- Apply Button with Icon Support -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/applyButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/colorr_button"
        android:clickable="true"
        android:focusable="true"
        android:minHeight="48dp"
        app:layout_constraintBottom_toTopOf="@id/bannerAdContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/applyButtonIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_ad"
                android:visibility="gone" />

            <TextView
                android:id="@+id/applyButtonText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/apply_customization"
                android:textColor="?attr/grey"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
