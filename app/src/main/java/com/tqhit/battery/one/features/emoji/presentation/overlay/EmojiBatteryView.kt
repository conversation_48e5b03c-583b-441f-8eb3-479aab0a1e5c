package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.data.service.EmojiItemService
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.max

/**
 * Custom view for displaying emoji battery overlay.
 * Implements horizontal bar layout with left and right groups as specified in overlayui.md
 * 
 * Layout Structure:
 * - Main Container: Horizontal bar with rounded corners and white background
 * - Left Group: Time, Silent Mode, Emoji (aligned to start)
 * - Right Group: Cellular Signal, WiFi Signal, Battery Indicator (aligned to end)
 * - Battery Indicator: Composite view with battery outline, fill, heart icon, and percentage text
 * 
 * This view follows the established patterns in the app:
 * - Uses custom drawing with Canvas and Paint
 * - Integrates with existing battery status data
 * - Supports customization configuration
 * - Follows Material 3 design guidelines
 * - Optimized for overlay display performance
 * - Supports swipe down gesture for notification panel
 */
class EmojiBatteryView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    companion object {
        private const val TAG = "EmojiBatteryView"
        private const val EMOJI_VIEW_TAG = "EmojiView_Rendering"
        private const val EMOJI_DRAW_TAG = "EmojiView_Drawing"
        private const val EMOJI_UPDATE_TAG = "EmojiView_Updates"
        private const val EMOJI_GESTURE_TAG = "EmojiView_Gesture"
        private const val EMOJI_THEME_TAG = "EmojiView_Theme"
        private const val EMOJI_COLOR_TAG = "EmojiView_Color"

        // Note: All dimensions and colors now moved to XML resources for better maintainability
    }
    
    // Paint objects for drawing
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        typeface = Typeface.DEFAULT_BOLD
        textAlign = Paint.Align.LEFT
        color = ContextCompat.getColor(context, R.color.emoji_text_color)
    }

    private val emojiPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        typeface = Typeface.DEFAULT
        textAlign = Paint.Align.LEFT
        textSize = resources.getDimension(R.dimen.emoji_overlay_emoji_size)
    }

    private val batteryPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
        color = ContextCompat.getColor(context, R.color.emoji_battery_percentage)
    }

    private val batteryStrokePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = 2f
        color = ContextCompat.getColor(context, R.color.emoji_battery_outline)
    }

    private val heartPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
        color = ContextCompat.getColor(context, R.color.emoji_heart_icon)
        alpha = 180  // Semi-transparent
    }
    
    // Dimensions (loaded from resources)
    private val defaultHeight = resources.getDimensionPixelSize(R.dimen.emoji_overlay_height)
    private val defaultEmojiSize = resources.getDimensionPixelSize(R.dimen.emoji_overlay_emoji_size)
    private val defaultBatteryWidth = resources.getDimensionPixelSize(R.dimen.emoji_overlay_battery_width)
    private val defaultBatteryHeight = resources.getDimensionPixelSize(R.dimen.emoji_overlay_battery_height)
    private val elementSpacing = resources.getDimensionPixelSize(R.dimen.emoji_overlay_element_spacing)
    private val horizontalPadding = resources.getDimensionPixelSize(R.dimen.emoji_overlay_horizontal_padding)
    private val groupSpacing = resources.getDimensionPixelSize(R.dimen.emoji_overlay_group_spacing)
    
    // Current state
    private var batteryStatus: CoreBatteryStatus? = null
    private var batteryStyle: BatteryStyle? = null
    private var customizationConfig: CustomizationConfig? = null
    private var statusBarInfo: CustomStatusBarInfo? = null
    
    // Cached bitmaps for performance
    private var emojiBitmap: Bitmap? = null
    private var batteryBitmap: Bitmap? = null

    // Image loading dependencies
    private var emojiItemService: EmojiItemService? = null
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    
    // Drawing rectangles for battery indicator
    private val batteryRect = RectF()
    private val batteryFillRect = RectF()
    private val batteryTerminalRect = RectF()
    private val heartRect = RectF()
    
    // Advanced gesture detection using helper class
    private val emojiGestureHelper = EmojiGestureHelper()

    // Gesture detection for advanced swipe down with left/right half detection
    private val gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
        override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
            BatteryLogger.d(EMOJI_GESTURE_TAG, "Fling gesture detected - analyzing with gesture helper")

            // Get screen width for position calculations
            val screenWidth = resources.displayMetrics.widthPixels

            // Use gesture helper to analyze the gesture
            val gestureResult = emojiGestureHelper.analyzeGesture(e1, e2, velocityX, velocityY, screenWidth)

            // Handle the gesture based on the result
            return handleGestureResult(gestureResult)
        }
    })
    
    init {
        setupDefaultColors()
        setupBackground()
        BatteryLogger.d(TAG, "EmojiBatteryView initialized with horizontal bar layout")
        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_VIEW_CREATED")
    }

    /**
     * Sets up the background drawable for status bar integration
     * Enhanced with comprehensive theme detection and logging
     */
    private fun setupBackground() {
        BatteryLogger.d(EMOJI_THEME_TAG, "BACKGROUND_SETUP_STARTED: Setting up emoji overlay background")

        // Enhanced theme detection
        val isDark = isDarkTheme()
        val appTheme = getAppTheme()
        val systemNightMode = resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK

        BatteryLogger.d(EMOJI_THEME_TAG, "BACKGROUND_THEME_STATE:")
        BatteryLogger.d(EMOJI_THEME_TAG, "  appTheme: $appTheme")
        BatteryLogger.d(EMOJI_THEME_TAG, "  isDark: $isDark")
        BatteryLogger.d(EMOJI_THEME_TAG, "  systemNightMode: $systemNightMode")

        // Load background drawable that automatically adapts to light/dark theme
        val backgroundDrawable = ContextCompat.getDrawable(context, R.drawable.emoji_overlay_background)
        BatteryLogger.d(EMOJI_VIEW_TAG, "Background drawable loaded: ${backgroundDrawable != null}")

        if (backgroundDrawable != null) {
            background = backgroundDrawable
            BatteryLogger.d(EMOJI_VIEW_TAG, "Background drawable applied successfully")
            BatteryLogger.d(EMOJI_VIEW_TAG, "Background drawable type: ${backgroundDrawable.javaClass.simpleName}")
        } else {
            BatteryLogger.e(EMOJI_VIEW_TAG, "Failed to load background drawable!")
            BatteryLogger.e(EMOJI_VIEW_TAG, "EMOJI_BACKGROUND_LOAD_FAILED")
        }

        // Set elevation for status bar integration
        val elevationPx = 4f * resources.displayMetrics.density
        elevation = elevationPx
        BatteryLogger.d(EMOJI_VIEW_TAG, "Elevation set to: ${elevationPx}px (4dp)")

        // Add padding for better visual separation from background
        val paddingHorizontal = resources.getDimensionPixelSize(R.dimen.emoji_overlay_horizontal_padding)
        val paddingVertical = (4 * resources.displayMetrics.density).toInt()
        setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical)
        BatteryLogger.d(EMOJI_VIEW_TAG, "Padding applied - horizontal: ${paddingHorizontal}px, vertical: ${paddingVertical}px")

        // Verify background is set
        val currentBackground = background
        BatteryLogger.d(EMOJI_VIEW_TAG, "Background verification - current background: ${currentBackground != null}")
        if (currentBackground != null) {
            BatteryLogger.d(EMOJI_VIEW_TAG, "Background bounds: ${currentBackground.bounds}")
            BatteryLogger.d(EMOJI_VIEW_TAG, "Background alpha: ${currentBackground.alpha}")
        }

        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_BACKGROUND_SETUP_COMPLETED")
    }

    /**
     * Helper method to determine if the current theme is dark
     * Enhanced to work with the app's custom theme system
     */
    private fun isDarkTheme(): Boolean {
        val nightModeFlags = resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK
        val systemDarkMode = nightModeFlags == android.content.res.Configuration.UI_MODE_NIGHT_YES

        // Get the app's current theme preference
        val appTheme = getAppTheme()
        val isDark = when (appTheme) {
            "BlackTheme", "BlackThemeInverted", "AmoledTheme", "AmoledThemeInverted" -> true
            "LightTheme", "LightThemeInverted" -> false
            "GreyTheme", "GreyThemeInverted" -> true // Treat grey themes as dark
            "AutoTheme" -> systemDarkMode
            else -> systemDarkMode
        }

        BatteryLogger.d(EMOJI_THEME_TAG, "THEME_DETECTION: appTheme=$appTheme, systemDark=$systemDarkMode, resultDark=$isDark")
        return isDark
    }

    /**
     * Gets the current app theme from ThemeManager
     */
    private fun getAppTheme(): String {
        return try {
            // Access ThemeManager to get current theme
            val sharedPrefs = context.getSharedPreferences("theme_preferences", Context.MODE_PRIVATE)
            val theme = sharedPrefs.getString("selected_theme", "AutoTheme") ?: "AutoTheme"
            BatteryLogger.d(EMOJI_THEME_TAG, "APP_THEME_RETRIEVED: $theme")
            theme
        } catch (e: Exception) {
            BatteryLogger.e(EMOJI_THEME_TAG, "ERROR_GETTING_APP_THEME", e)
            "AutoTheme"
        }
    }

    /**
     * Sets up default colors based on theme for proper contrast and accessibility
     * Enhanced with comprehensive logging and proper theme detection
     */
    private fun setupDefaultColors() {
        BatteryLogger.d(EMOJI_COLOR_TAG, "SETUP_COLORS_STARTED: Setting up emoji view colors with enhanced theme detection")

        // Enhanced theme detection using app's theme system
        val isDark = isDarkTheme()
        val appTheme = getAppTheme()

        BatteryLogger.d(EMOJI_COLOR_TAG, "THEME_STATE: appTheme=$appTheme, isDark=$isDark")

        // Get theme-aware colors from resources
        val textColor = ContextCompat.getColor(context, R.color.emoji_text_color)
        val iconColor = ContextCompat.getColor(context, R.color.emoji_status_icon_color)
        val batteryOutlineColor = ContextCompat.getColor(context, R.color.emoji_battery_outline)
        val accentColor = ContextCompat.getColor(context, R.color.emoji_battery_percentage)
        val heartColor = ContextCompat.getColor(context, R.color.emoji_heart_icon)

        // Log all color values for debugging
        BatteryLogger.d(EMOJI_COLOR_TAG, "COLOR_VALUES:")
        BatteryLogger.d(EMOJI_COLOR_TAG, "  textColor: ${String.format("#%08X", textColor)}")
        BatteryLogger.d(EMOJI_COLOR_TAG, "  iconColor: ${String.format("#%08X", iconColor)}")
        BatteryLogger.d(EMOJI_COLOR_TAG, "  batteryOutlineColor: ${String.format("#%08X", batteryOutlineColor)}")
        BatteryLogger.d(EMOJI_COLOR_TAG, "  accentColor: ${String.format("#%08X", accentColor)}")
        BatteryLogger.d(EMOJI_COLOR_TAG, "  heartColor: ${String.format("#%08X", heartColor)}")

        // Configure text paint for time and percentage with theme-appropriate colors
        textPaint.color = textColor
        textPaint.textSize = resources.getDimension(R.dimen.emoji_overlay_text_size)
        textPaint.typeface = Typeface.DEFAULT_BOLD
        BatteryLogger.d(EMOJI_COLOR_TAG, "TEXT_PAINT_CONFIGURED: color=${String.format("#%08X", textPaint.color)}, size=${textPaint.textSize}")

        // Configure battery indicator colors
        batteryPaint.color = accentColor
        batteryStrokePaint.color = batteryOutlineColor
        heartPaint.color = heartColor

        BatteryLogger.d(EMOJI_COLOR_TAG, "BATTERY_PAINT_CONFIGURED:")
        BatteryLogger.d(EMOJI_COLOR_TAG, "  batteryPaint.color: ${String.format("#%08X", batteryPaint.color)}")
        BatteryLogger.d(EMOJI_COLOR_TAG, "  batteryStrokePaint.color: ${String.format("#%08X", batteryStrokePaint.color)}")
        BatteryLogger.d(EMOJI_COLOR_TAG, "  heartPaint.color: ${String.format("#%08X", heartPaint.color)}")

        BatteryLogger.d(EMOJI_COLOR_TAG, "SETUP_COLORS_COMPLETED: All colors configured successfully")
    }
    
    /**
     * Handles the result of advanced gesture analysis for Task 4.2 implementation.
     *
     * This method processes gesture results from EmojiGestureHelper and triggers
     * appropriate system actions through the NotificationActionCallback interface.
     *
     * **Gesture Processing:**
     * - SWIPE_DOWN_LEFT → Request notification panel via callback
     * - SWIPE_DOWN_RIGHT → Request quick settings panel via callback
     * - SWIPE_DOWN_CENTER → Request notification panel (default) via callback
     * - NONE → No action taken, gesture ignored
     *
     * **Architecture:**
     * - Uses callback pattern to communicate with accessibility service
     * - Falls back to legacy notification panel if no callback is set
     * - Comprehensive logging for debugging and monitoring
     * - Returns boolean to indicate if gesture was handled
     *
     * @param gestureResult The result from EmojiGestureHelper analysis containing gesture type and details
     * @return true if the gesture was handled and action was requested, false otherwise
     * @see EmojiGestureHelper.GestureResult
     * @see NotificationActionCallback.onNotificationActionRequested
     */
    private fun handleGestureResult(gestureResult: EmojiGestureHelper.GestureResult): Boolean {
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Handling gesture result: ${gestureResult.gestureType}")

        when (gestureResult.gestureType) {
            EmojiGestureHelper.GestureType.SWIPE_DOWN_LEFT -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Left swipe detected - requesting notification panel")
                requestNotificationAction(EmojiGestureHelper.GestureType.SWIPE_DOWN_LEFT)
                return true
            }
            EmojiGestureHelper.GestureType.SWIPE_DOWN_RIGHT -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Right swipe detected - requesting quick settings")
                requestNotificationAction(EmojiGestureHelper.GestureType.SWIPE_DOWN_RIGHT)
                return true
            }
            EmojiGestureHelper.GestureType.SWIPE_DOWN_CENTER -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Center swipe detected - requesting notification panel (default)")
                requestNotificationAction(EmojiGestureHelper.GestureType.SWIPE_DOWN_CENTER)
                return true
            }
            EmojiGestureHelper.GestureType.NONE -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "No valid gesture detected")
                return false
            }
        }
    }

    /**
     * Callback interface for advanced gesture communication with accessibility service.
     *
     * This interface enables EmojiBatteryView to communicate gesture detection results
     * to the EmojiBatteryAccessibilityService for Task 4.2 implementation.
     *
     * **Design Pattern:**
     * - Implements callback pattern for loose coupling between view and service
     * - Allows view to remain independent of accessibility service implementation
     * - Enables testability by allowing mock implementations
     * - Supports future extensibility for additional gesture types
     *
     * **Usage:**
     * - Set via EmojiBatteryView.setNotificationActionCallback()
     * - Called from handleGestureResult() when valid gestures are detected
     * - Implemented by EmojiBatteryAccessibilityService.onNotificationActionRequested()
     *
     * @see EmojiBatteryAccessibilityService.onNotificationActionRequested
     * @see EmojiGestureHelper.GestureType
     */
    interface NotificationActionCallback {
        /**
         * Called when a gesture requires a notification action to be performed.
         *
         * @param gestureType The type of gesture that was detected and should trigger an action
         */
        fun onNotificationActionRequested(gestureType: EmojiGestureHelper.GestureType)
    }

    // Callback for communicating with the accessibility service
    private var notificationActionCallback: NotificationActionCallback? = null

    /**
     * Sets the callback for notification actions.
     * This allows the view to communicate with the accessibility service.
     */
    fun setNotificationActionCallback(callback: NotificationActionCallback?) {
        this.notificationActionCallback = callback
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Notification action callback set: ${callback != null}")
    }

    /**
     * Requests a notification action through the callback.
     * Falls back to legacy method if no callback is set.
     */
    private fun requestNotificationAction(gestureType: EmojiGestureHelper.GestureType) {
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Requesting notification action for gesture: $gestureType")

        if (notificationActionCallback != null) {
            BatteryLogger.d(EMOJI_GESTURE_TAG, "Using callback to request notification action")
            notificationActionCallback?.onNotificationActionRequested(gestureType)
        } else {
            BatteryLogger.w(EMOJI_GESTURE_TAG, "No callback set - falling back to legacy notification panel")
            openNotificationPanelLegacy()
        }
    }

    /**
     * Legacy method for opening notification panel using system intent.
     * Kept for backward compatibility when accessibility service is not available.
     */
    private fun openNotificationPanelLegacy() {
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Opening notification panel via legacy system intent")
        try {
            // Use system intent to open notification panel
            val intent = context.packageManager.getLaunchIntentForPackage("com.android.systemui")
            if (intent != null) {
                intent.action = "android.intent.action.OPEN_NOTIFICATION_PANEL"
                context.startActivity(intent)
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Legacy notification panel intent sent")
            } else {
                BatteryLogger.w(EMOJI_GESTURE_TAG, "Could not create legacy notification panel intent")
            }
        } catch (exception: Exception) {
            BatteryLogger.e(EMOJI_GESTURE_TAG, "Error opening legacy notification panel", exception)
        }
    }

    /**
     * Updates the battery status and triggers a redraw
     * Enhanced to ensure proper synchronization with real-time battery data
     */
    fun updateBatteryStatus(status: CoreBatteryStatus) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "Updating emoji battery status: ${status.percentage}%, charging: ${status.isCharging}")
        BatteryLogger.logBatteryStatus(
            EMOJI_UPDATE_TAG,
            status.percentage,
            status.isCharging,
            status.currentMicroAmperes,
            status.voltageMillivolts,
            status.temperatureCelsius
        )

        // Store the previous status to detect changes
        val previousStatus = this.batteryStatus
        this.batteryStatus = status
        
        // Log battery percentage changes for debugging synchronization
        if (previousStatus != null && previousStatus.percentage != status.percentage) {
            BatteryLogger.d(EMOJI_UPDATE_TAG, "BATTERY_SYNC: Battery percentage changed from ${previousStatus.percentage}% to ${status.percentage}%")
        }
        
        // Force immediate redraw to ensure UI synchronization
        post {
            invalidate()
        }
        
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_BATTERY_STATUS_UPDATED: ${status.percentage}%")
    }

    /**
     * Updates the battery style and triggers a redraw
     */
    fun updateBatteryStyle(style: BatteryStyle) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "Updating emoji battery style: ${style.name}")
        this.batteryStyle = style

        // Clear cached bitmaps to force reload
        emojiBitmap = null
        batteryBitmap = null

        invalidate()
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_BATTERY_STYLE_UPDATED: ${style.name}")
    }

    /**
     * Sets the EmojiItemService dependency for loading battery and emoji images
     */
    fun setEmojiItemService(service: EmojiItemService) {
        this.emojiItemService = service
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: EmojiItemService dependency set")
    }

    /**
     * Updates the customization configuration and triggers a redraw
     */
    fun updateCustomizationConfig(config: CustomizationConfig) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "Updating emoji customization config")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: ========== VIEW CONFIG UPDATE STARTED ==========")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - selectedStyleId: ${config.selectedStyleId}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - showEmoji: ${config.customConfig.showEmoji}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - showPercentage: ${config.customConfig.showPercentage}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - percentageFontSize: ${config.customConfig.percentageFontSizeDp}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - emojiSizeScale: ${config.customConfig.emojiSizeScale}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - percentageColor: ${config.customConfig.percentageColor}")

        this.customizationConfig = config
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: Updated customizationConfig field")

        // Update paint properties based on config
        val oldTextColor = textPaint.color
        val oldTextSize = textPaint.textSize

        // Use theme-aware color instead of hardcoded config color for better contrast
        val themeAwareTextColor = ContextCompat.getColor(context, R.color.emoji_text_color)
        textPaint.color = themeAwareTextColor
        textPaint.textSize = config.customConfig.percentageFontSizeDp * resources.displayMetrics.scaledDensity

        BatteryLogger.d(EMOJI_COLOR_TAG, "CONFIG_UPDATE_COLORS: Using theme-aware color instead of config color")
        BatteryLogger.d(EMOJI_COLOR_TAG, "CONFIG_UPDATE_COLORS: configColor=${String.format("#%08X", config.customConfig.percentageColor)}, themeColor=${String.format("#%08X", themeAwareTextColor)}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: Updated paint - color: $oldTextColor -> ${textPaint.color}, size: $oldTextSize -> ${textPaint.textSize}")

        BatteryLogger.d(EMOJI_UPDATE_TAG, "Config updated - show emoji: ${config.customConfig.showEmoji}, show percentage: ${config.customConfig.showPercentage}")

        // Load images for the new configuration
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: Loading images for new configuration")
        loadImagesForConfiguration(config)

        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: Calling invalidate() to trigger redraw")
        invalidate()
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_CUSTOMIZATION_CONFIG_UPDATED")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: ========== VIEW CONFIG UPDATE COMPLETED ==========")
    }

    /**
     * Updates the custom status bar information and triggers a redraw
     */
    fun updateStatusBarInfo(info: CustomStatusBarInfo) {
        BatteryLogger.d(TAG, "Updating custom status bar info: time=${info.currentTime}, wifi=${info.wifiSignalStrength}, cellular=${info.cellularSignalStrength}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "STATUS_BAR_INFO: Time=${info.currentTime}, WiFi=${info.hasWifiConnection}, Cellular=${info.hasCellularConnection}, Silent=${info.isRingerSilent}")

        this.statusBarInfo = info
        invalidate()
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_STATUS_BAR_INFO_UPDATED")
    }

    /**
     * Updates background and colors when theme changes
     * Enhanced with comprehensive logging and theme state tracking
     */
    fun updateTheme() {
        BatteryLogger.d(EMOJI_THEME_TAG, "THEME_UPDATE_STARTED: Updating emoji overlay theme")

        // Log current theme state before update
        val oldAppTheme = getAppTheme()
        val oldIsDark = isDarkTheme()
        BatteryLogger.d(EMOJI_THEME_TAG, "THEME_UPDATE_BEFORE: appTheme=$oldAppTheme, isDark=$oldIsDark")

        // Force clear current background before applying new one
        val oldBackground = background
        background = null
        BatteryLogger.d(EMOJI_THEME_TAG, "BACKGROUND_CLEARED: oldBackground=${oldBackground != null}")

        // Refresh background drawable and colors for new theme
        setupBackground()
        setupDefaultColors()

        // Log new theme state after update
        val newAppTheme = getAppTheme()
        val newIsDark = isDarkTheme()
        BatteryLogger.d(EMOJI_THEME_TAG, "THEME_UPDATE_AFTER: appTheme=$newAppTheme, isDark=$newIsDark")

        // Log theme change detection
        val themeChanged = oldAppTheme != newAppTheme || oldIsDark != newIsDark
        BatteryLogger.d(EMOJI_THEME_TAG, "THEME_CHANGE_DETECTED: $themeChanged")

        // Force layout and redraw with new theme
        requestLayout()
        invalidate()

        BatteryLogger.d(EMOJI_THEME_TAG, "THEME_UPDATE_COMPLETED: Theme update finished")
    }

    /**
     * Forces background refresh - useful for debugging background issues
     */
    fun forceBackgroundRefresh() {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_BACKGROUND_FORCE_REFRESH_STARTED")

        // Clear current background
        background = null

        // Reapply background
        setupBackground()

        // Force redraw
        invalidate()

        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_BACKGROUND_FORCE_REFRESH_COMPLETED")
    }
    
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_STARTED")
        
        val config = customizationConfig ?: CustomizationConfig.createDefault()
        val status = batteryStatus ?: CoreBatteryStatus.createDefault()

        // For full-width overlay, use the entire available screen width
        val screenWidth = resources.displayMetrics.widthPixels
        val requiredHeight = defaultHeight

        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_SCREEN_WIDTH: ${screenWidth}px")
        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_REQUIRED_HEIGHT: ${requiredHeight}px")
        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_PADDING: horizontal=${horizontalPadding}px, vertical=${paddingTop}px")

        // Calculate minimum required width for all elements
        var minRequiredWidth = horizontalPadding * 2

        // Left group minimum width (Time + Emoji)  
        var leftGroupMinWidth = 0
        leftGroupMinWidth += measureTimeWidth().toInt() + elementSpacing
        if (config.customConfig.showEmoji) {
            leftGroupMinWidth += (defaultEmojiSize * config.customConfig.emojiSizeScale).toInt() + elementSpacing
        }

        // Right group minimum width (WiFi + Cellular + Silent + Battery)
        var rightGroupMinWidth = 0
        rightGroupMinWidth += measureWifiIconWidth().toInt() + elementSpacing
        rightGroupMinWidth += measureCellularIconWidth().toInt() + elementSpacing  
        rightGroupMinWidth += measureSilentIconWidth().toInt() + elementSpacing
        rightGroupMinWidth += defaultBatteryWidth
        if (config.customConfig.showPercentage) {
            rightGroupMinWidth += measurePercentageWidth(status).toInt() + elementSpacing
        }

        minRequiredWidth += leftGroupMinWidth + rightGroupMinWidth + groupSpacing

        // Use full screen width if available, or minimum required width
        val finalWidth = max(screenWidth, minRequiredWidth)
        val finalHeight = resolveSize(requiredHeight, heightMeasureSpec)

        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_CALCULATIONS: leftGroup=${leftGroupMinWidth}px, rightGroup=${rightGroupMinWidth}px, minRequired=${minRequiredWidth}px")
        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_FINAL_DIMENSIONS: ${finalWidth}x${finalHeight}px")
        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_COMPLETED")

        setMeasuredDimension(finalWidth, finalHeight)
    }
    
    override fun onDraw(canvas: Canvas) {
        // Log background state before drawing
        val currentBackground = background
        BatteryLogger.v(EMOJI_DRAW_TAG, "Pre-draw background check - background: ${currentBackground != null}")
        if (currentBackground != null) {
            BatteryLogger.v(EMOJI_DRAW_TAG, "Background visible: ${currentBackground.isVisible}, alpha: ${currentBackground.alpha}")
        }

        super.onDraw(canvas)

        BatteryLogger.v(EMOJI_DRAW_TAG, "Drawing emoji battery view with full-width layout")
        BatteryLogger.v(EMOJI_DRAW_TAG, "View dimensions: ${width}x${height}, padding: ${paddingLeft},${paddingTop},${paddingRight},${paddingBottom}")

        val config = customizationConfig ?: CustomizationConfig.createDefault()
        val status = batteryStatus ?: CoreBatteryStatus.createDefault()
        val statusInfo = statusBarInfo ?: CustomStatusBarInfo.fromSystemState(context)

        val centerY = height / 2f

        BatteryLogger.v(EMOJI_DRAW_TAG, "Drawing full-width elements - emoji: ${config.customConfig.showEmoji}, percentage: ${config.customConfig.showPercentage}, battery: ${status.percentage}%")

        // Full-width layout: distribute elements across entire width
        drawFullWidthLayout(canvas, centerY, statusInfo, status, config)

        BatteryLogger.v(EMOJI_DRAW_TAG, "EMOJI_VIEW_DRAW_COMPLETED")
    }

    /**
     * Draws the full-width layout with proper element distribution
     */
    private fun drawFullWidthLayout(canvas: Canvas, centerY: Float, statusInfo: CustomStatusBarInfo, status: CoreBatteryStatus, config: CustomizationConfig) {
        val totalWidth = width.toFloat()
        val leftMargin = horizontalPadding.toFloat()
        val rightMargin = horizontalPadding.toFloat()
        val availableWidth = totalWidth - leftMargin - rightMargin

        BatteryLogger.d(EMOJI_DRAW_TAG, "Starting full-width layout - totalWidth: $totalWidth, available: $availableWidth")

        // Calculate widths for each section - NEW LAYOUT: time+emoji on left, all status icons on right
        val leftGroupWidth = calculateNewLeftGroupWidth(statusInfo, config)
        val rightGroupWidth = calculateNewRightGroupWidth(statusInfo, status, config)
        val centerSpaceWidth = availableWidth - leftGroupWidth - rightGroupWidth

        BatteryLogger.d(EMOJI_DRAW_TAG, "Layout distribution - left: $leftGroupWidth, center: $centerSpaceWidth, right: $rightGroupWidth")

        // Draw left group (Time + Emoji) - aligned to left
        var currentX = leftMargin
        currentX += drawNewLeftGroup(canvas, currentX, centerY, statusInfo, config)

        // Draw right group (All status icons: WiFi + Cellular + Ring/Silent + Battery) - aligned to right  
        val rightStartX = totalWidth - rightMargin - rightGroupWidth
        drawNewRightGroup(canvas, rightStartX, centerY, statusInfo, status, config)
        
        BatteryLogger.d(EMOJI_DRAW_TAG, "Layout completed - left group at $leftMargin, right group at $rightStartX")
    }

    /**
     * Calculates the width required for the NEW left group (Time + Emoji)
     */
    private fun calculateNewLeftGroupWidth(statusInfo: CustomStatusBarInfo, config: CustomizationConfig): Float {
        var width = 0f
        // Time width
        width += measureTimeWidth() + elementSpacing
        // Emoji width (if enabled)
        if (config.customConfig.showEmoji) {
            width += (defaultEmojiSize * config.customConfig.emojiSizeScale).toInt() + elementSpacing
        }
        BatteryLogger.d(EMOJI_DRAW_TAG, "Left group width calculation: time + emoji = $width")
        return width
    }

    /**
     * Calculates the width required for the NEW right group (All status icons)
     */
    private fun calculateNewRightGroupWidth(statusInfo: CustomStatusBarInfo, status: CoreBatteryStatus, config: CustomizationConfig): Float {
        var width = 0f
        // WiFi icon
        if (statusInfo.hasWifiConnection) {
            width += measureWifiIconWidth() + elementSpacing
        }
        // Cellular icon  
        if (statusInfo.hasCellularConnection) {
            width += measureCellularIconWidth() + elementSpacing
        }
        // Ring/Silent icon
        if (statusInfo.isRingerSilent) {
            width += measureSilentIconWidth() + elementSpacing
        }
        // Battery indicator
        width += defaultBatteryWidth
        // Battery percentage (if enabled)
        if (config.customConfig.showPercentage) {
            width += measurePercentageWidth(status) + elementSpacing
        }
        BatteryLogger.d(EMOJI_DRAW_TAG, "Right group width calculation: all status icons = $width")
        return width
    }

    /**
     * Draws the NEW left group (Time + Emoji)
     */
    private fun drawNewLeftGroup(canvas: Canvas, x: Float, centerY: Float, statusInfo: CustomStatusBarInfo, config: CustomizationConfig): Float {
        var currentX = x
        BatteryLogger.d(EMOJI_DRAW_TAG, "Drawing new left group starting at x=$currentX")

        // Draw time (leftmost element)
        val timeWidth = drawTime(canvas, currentX, centerY, statusInfo.currentTime)
        currentX += timeWidth + elementSpacing
        BatteryLogger.d(EMOJI_DRAW_TAG, "Drew time, moved to x=$currentX")

        // Draw emoji (next to time)
        if (config.customConfig.showEmoji) {
            val emojiWidth = drawEmoji(canvas, currentX, centerY, config)
            currentX += emojiWidth + elementSpacing
            BatteryLogger.d(EMOJI_DRAW_TAG, "Drew emoji, moved to x=$currentX")
        }

        val totalWidth = currentX - x
        BatteryLogger.d(EMOJI_DRAW_TAG, "Left group completed, total width: $totalWidth")
        return totalWidth
    }

    /**
     * Draws the NEW right group (All status icons: WiFi + Cellular + Ring/Silent + Battery)
     */
    private fun drawNewRightGroup(canvas: Canvas, x: Float, centerY: Float, statusInfo: CustomStatusBarInfo, status: CoreBatteryStatus, config: CustomizationConfig): Float {
        var currentX = x
        BatteryLogger.d(EMOJI_DRAW_TAG, "Drawing new right group starting at x=$currentX")

        // Draw WiFi signal (first in right group)
        if (statusInfo.hasWifiConnection) {
            val wifiWidth = drawWifiIcon(canvas, currentX, centerY, statusInfo.wifiSignalStrength)
            currentX += wifiWidth + elementSpacing
            BatteryLogger.d(EMOJI_DRAW_TAG, "Drew WiFi icon, moved to x=$currentX")
        }

        // Draw cellular signal
        if (statusInfo.hasCellularConnection) {
            val cellularWidth = drawCellularIcon(canvas, currentX, centerY, statusInfo.cellularSignalStrength)
            currentX += cellularWidth + elementSpacing
            BatteryLogger.d(EMOJI_DRAW_TAG, "Drew cellular icon, moved to x=$currentX")
        }

        // Draw ring/silent mode icon
        if (statusInfo.isRingerSilent) {
            val silentWidth = drawSilentModeIcon(canvas, currentX, centerY)
            currentX += silentWidth + elementSpacing
            BatteryLogger.d(EMOJI_DRAW_TAG, "Drew silent mode icon, moved to x=$currentX")
        }

        // Draw battery indicator (rightmost element)
        val batteryWidth = drawBatteryIndicator(canvas, currentX, centerY, status, config)
        currentX += batteryWidth
        BatteryLogger.d(EMOJI_DRAW_TAG, "Drew battery indicator, final x=$currentX")

        val totalWidth = currentX - x
        BatteryLogger.d(EMOJI_DRAW_TAG, "Right group completed, total width: $totalWidth")
        return totalWidth
    }

    /**
     * Draws the time display with theme-appropriate colors
     */
    private fun drawTime(canvas: Canvas, x: Float, centerY: Float, time: String): Float {
        // Ensure we use theme-aware color for time text
        val themeAwareTextColor = ContextCompat.getColor(context, R.color.emoji_text_color)

        // Create time-specific paint with theme-appropriate colors
        val timePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = themeAwareTextColor
            textSize = (12 * resources.displayMetrics.scaledDensity) // Slightly larger for full-width layout
            typeface = Typeface.DEFAULT_BOLD
            textAlign = Paint.Align.LEFT
        }

        // Calculate text bounds for positioning
        val textBounds = Rect()
        timePaint.getTextBounds(time, 0, time.length, textBounds)

        val textY = centerY + (textBounds.height() / 2f) - textBounds.bottom
        canvas.drawText(time, x, textY, timePaint)

        BatteryLogger.v(EMOJI_DRAW_TAG, "Drew time '$time' at ($x, $textY) with theme-aware color ${String.format("#%08X", themeAwareTextColor)}")
        return textBounds.width().toFloat()
    }

    /**
     * Draws the silent mode icon with theme-appropriate colors
     */
    private fun drawSilentModeIcon(canvas: Canvas, x: Float, centerY: Float): Float {
        return drawStatusIcon(
            canvas = canvas,
            x = x,
            centerY = centerY,
            drawableResourceId = R.drawable.ic_custom_ring_silent,
            iconType = "SILENT",
            signalStrength = null
        )
    }
    
    /**
     * Draws the emoji at the specified position using actual emoji character
     */
    private fun drawEmoji(canvas: Canvas, x: Float, centerY: Float, config: CustomizationConfig): Float {
        val scaleFactor = 1.5f
        val emojiSize = (defaultEmojiSize * config.customConfig.emojiSizeScale).toInt() * scaleFactor // Increase by 150%

        // Get emoji character from battery style or use default laughing emoji from screenshot
        val emojiCharacter = getEmojiCharacter(config)

        // Configure emoji paint with proper size
        emojiPaint.textSize = emojiSize.toFloat()

        // Calculate text bounds for proper positioning
        val textBounds = Rect()
        emojiPaint.getTextBounds(emojiCharacter, 0, emojiCharacter.length, textBounds)

        // Calculate position to center emoji vertically
        val emojiX = x
        val emojiY = centerY + (textBounds.height() / 2f) - textBounds.bottom

        // Draw the actual emoji character
        canvas.drawText(emojiCharacter, emojiX, emojiY, emojiPaint)

        BatteryLogger.v(EMOJI_DRAW_TAG, "Drew emoji '$emojiCharacter' at ($emojiX, $emojiY) with size $emojiSize")
        BatteryLogger.d("ICON_SIZE_TEST", "Emoji text icon size: $emojiSize")

        return textBounds.width().toFloat()
    }

    /**
     * Loads battery and emoji images for the given configuration
     */
    private fun loadImagesForConfiguration(config: CustomizationConfig) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: ========== LOADING IMAGES FOR CONFIG ==========")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: selectedStyleId: ${config.selectedStyleId}")

        // VISUAL_INCONSISTENCY_DEBUG: Log overlay configuration details
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: ========== OVERLAY IMAGE LOADING STARTED ==========")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY Config Details:")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Selected Style ID: ${config.selectedStyleId}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Battery Image URL: ${config.batteryImageUrl}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Emoji Image URL: ${config.emojiImageUrl}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Battery Style Name: ${config.batteryStyleName}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Emoji Style Name: ${config.emojiStyleName}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Has Direct URLs: ${config.hasDirectUrls()}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Should Use Direct URLs: ${config.shouldUseDirectUrls()}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Show Emoji: ${config.customConfig.showEmoji}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Show Percentage: ${config.customConfig.showPercentage}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Global Enabled: ${config.isGlobalEnabled}")

        // VISUAL_INCONSISTENCY_FIX: Check if we should use direct URLs instead of style ID resolution
        if (config.shouldUseDirectUrls()) {
            BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_FIX: Using direct URLs - bypassing style ID resolution")
            BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_FIX: Loading battery image from: ${config.batteryImageUrl}")
            BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_FIX: Loading emoji image from: ${config.emojiImageUrl}")

            loadBatteryImage(config.batteryImageUrl)
            loadEmojiImage(config.emojiImageUrl)
            return
        }

        if (config.selectedStyleId.isBlank()) {
            BatteryLogger.w(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: selectedStyleId is blank and no direct URLs, cannot load images")
            BatteryLogger.w(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY ERROR - No style ID or direct URLs provided")
            return
        }

        val service = emojiItemService
        if (service == null) {
            BatteryLogger.w(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: EmojiItemService is null, cannot load images")
            BatteryLogger.w(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY ERROR - EmojiItemService not available")
            return
        }

        // Parse composite style ID to get individual component IDs
        val (batteryStyleId, emojiStyleId) = if (config.selectedStyleId.contains("_")) {
            // Mixed components: "batteryId_emojiId"
            val parts = config.selectedStyleId.split("_", limit = 2)
            Pair(parts[0], parts[1])
        } else {
            // Same style for both components
            Pair(config.selectedStyleId, config.selectedStyleId)
        }

        BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Parsed IDs - battery: $batteryStyleId, emoji: $emojiStyleId")

        // VISUAL_INCONSISTENCY_DEBUG: Log style ID parsing details
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY Style ID Parsing:")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Original Composite ID: ${config.selectedStyleId}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Contains underscore? ${config.selectedStyleId.contains("_")}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Parsed Battery Style ID: $batteryStyleId")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Parsed Emoji Style ID: $emojiStyleId")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Same style for both? ${batteryStyleId == emojiStyleId}")

        // Load images asynchronously
        coroutineScope.launch {
            try {
                // Load all emoji items from all categories to find the specific styles
                val allEmojiItems = mutableListOf<com.tqhit.battery.one.features.emoji.domain.model.EmojiItem>()
                val categoryIds = service.getAvailableCategoryIds()

                BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Loading from ${categoryIds.size} categories")

                for (categoryId in categoryIds) {
                    val items = service.getEmojiItemsByCategory(categoryId)
                    allEmojiItems.addAll(items)
                }

                BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Loaded ${allEmojiItems.size} total emoji items")

                // Convert to BatteryStyle objects and find the specific styles
                val allStyles = allEmojiItems.map { it.toBatteryStyle() }
                val batteryStyle = allStyles.find { it.id == batteryStyleId }
                val emojiStyle = allStyles.find { it.id == emojiStyleId }

                BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Found battery style: ${batteryStyle != null}")
                BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Found emoji style: ${emojiStyle != null}")

                // VISUAL_INCONSISTENCY_DEBUG: Log detailed style resolution for overlay
                if (batteryStyle != null) {
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY Battery Style Found:")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Battery ID: ${batteryStyle.id}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Battery Name: ${batteryStyle.name}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Battery Image URL (used for overlay): ${batteryStyle.batteryImageUrl}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Alternative URLs available:")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG:   - Gallery Thumbnail: ${batteryStyle.galleryThumbnailUrl}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG:   - Customize Preview: ${batteryStyle.customizePreviewUrl}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG:   - Emoji Image: ${batteryStyle.emojiImageUrl}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Battery image URL: ${batteryStyle.batteryImageUrl}")
                    loadBatteryImage(batteryStyle.batteryImageUrl)
                } else {
                    BatteryLogger.w(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY ERROR - Battery style not found for ID: $batteryStyleId")
                }

                if (emojiStyle != null) {
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY Emoji Style Found:")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Emoji ID: ${emojiStyle.id}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Emoji Name: ${emojiStyle.name}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Emoji Image URL (used for overlay): ${emojiStyle.emojiImageUrl}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Alternative URLs available:")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG:   - Gallery Thumbnail: ${emojiStyle.galleryThumbnailUrl}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG:   - Customize Preview: ${emojiStyle.customizePreviewUrl}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG:   - Battery Image: ${emojiStyle.batteryImageUrl}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Emoji image URL: ${emojiStyle.emojiImageUrl}")
                    loadEmojiImage(emojiStyle.emojiImageUrl)
                } else {
                    BatteryLogger.w(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY ERROR - Emoji style not found for ID: $emojiStyleId")
                }

            } catch (exception: Exception) {
                BatteryLogger.e(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Error loading styles", exception)
                BatteryLogger.e(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY ERROR - Exception during image loading", exception)
            }
        }

        BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: ========== LOADING IMAGES INITIATED ==========")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: ========== OVERLAY IMAGE LOADING INITIATED ==========")
    }

    /**
     * Loads battery image from URL using Glide
     */
    private fun loadBatteryImage(imageUrl: String) {
        if (imageUrl.isBlank()) {
            BatteryLogger.w(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Battery image URL is blank")
            return
        }

        BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Loading battery image from: $imageUrl")

        Glide.with(context)
            .asBitmap()
            .load(imageUrl)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Battery image loaded successfully")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY Battery Image Loaded Successfully:")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - URL: $imageUrl")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Bitmap size: ${resource.width}x${resource.height}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - This bitmap will be used for overlay rendering")
                    batteryBitmap = resource
                    invalidate() // Trigger redraw with new battery image
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Battery image load cleared")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY Battery image cleared")
                    batteryBitmap = null
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    BatteryLogger.w(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Failed to load battery image from: $imageUrl")
                    BatteryLogger.w(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY ERROR - Failed to load battery image from: $imageUrl")
                    batteryBitmap = null
                }
            })
    }

    /**
     * Loads emoji image from URL using Glide
     */
    private fun loadEmojiImage(imageUrl: String) {
        if (imageUrl.isBlank()) {
            BatteryLogger.w(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Emoji image URL is blank")
            return
        }

        BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Loading emoji image from: $imageUrl")

        Glide.with(context)
            .asBitmap()
            .load(imageUrl)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Emoji image loaded successfully")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY Emoji Image Loaded Successfully:")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - URL: $imageUrl")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - Bitmap size: ${resource.width}x${resource.height}")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: - This bitmap will be used for overlay rendering")
                    emojiBitmap = resource
                    invalidate() // Trigger redraw with new emoji image
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Emoji image load cleared")
                    BatteryLogger.d(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY Emoji image cleared")
                    emojiBitmap = null
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    BatteryLogger.w(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Failed to load emoji image from: $imageUrl")
                    BatteryLogger.w(EMOJI_UPDATE_TAG, "VISUAL_INCONSISTENCY_DEBUG: OVERLAY ERROR - Failed to load emoji image from: $imageUrl")
                    emojiBitmap = null
                }
            })
    }

    /**
     * Gets the emoji character to display based on configuration
     * This is now a fallback when emoji image is not available
     */
    private fun getEmojiCharacter(config: CustomizationConfig): String {
        // Use laughing emoji from screenshot as default fallback
        // This is only used when emoji image bitmap is not available
        return "😂"  // Laughing emoji as shown in screenshot
    }

    /**
     * Draws WiFi signal strength icon with theme-appropriate colors
     */
    private fun drawWifiIcon(canvas: Canvas, x: Float, centerY: Float, signalStrength: Int): Float {
        return drawStatusIcon(
            canvas = canvas,
            x = x,
            centerY = centerY,
            drawableResourceId = R.drawable.ic_custom_wifi,
            iconType = "WIFI",
            signalStrength = signalStrength
        )
    }

    /**
     * Draws cellular signal strength icon with theme-appropriate colors
     */
    private fun drawCellularIcon(canvas: Canvas, x: Float, centerY: Float, signalStrength: Int): Float {
        return drawStatusIcon(
            canvas = canvas,
            x = x,
            centerY = centerY,
            drawableResourceId = R.drawable.ic_custom_cellular,
            iconType = "CELLULAR",
            signalStrength = signalStrength
        )
    }

    /**
     * Consolidated helper method for drawing status bar icons with consistent theming and positioning.
     * Eliminates code duplication between WiFi, cellular, and silent mode icons.
     *
     * @param canvas The canvas to draw on
     * @param x The x position for the icon
     * @param centerY The center y position for the icon
     * @param drawableResourceId The drawable resource ID for the icon
     * @param iconType The type of icon for logging purposes (e.g., "WIFI", "CELLULAR", "SILENT")
     * @param signalStrength Optional signal strength value for logging (used by WiFi and cellular icons)
     * @return The width of the drawn icon
     */
    private fun drawStatusIcon(
        canvas: Canvas,
        x: Float,
        centerY: Float,
        drawableResourceId: Int,
        iconType: String,
        signalStrength: Int? = null
    ): Float {
        val iconSize = (16 * resources.displayMetrics.density).toInt()  // Consistent 16dp size for all status icons
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Starting to create ${iconType.lowercase()} icon")
        
        val drawable = ContextCompat.getDrawable(context, drawableResourceId)
        if (drawable == null) {
            BatteryLogger.e(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: ERROR - Failed to load drawable resource")
            return iconSize.toFloat()
        }
        
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Drawable loaded successfully")
        
        // Get theme-aware icon color from resources
        val iconColor = ContextCompat.getColor(context, R.color.emoji_status_icon_color)
        val isLightTheme = !isDarkTheme()
        val strengthLog = if (signalStrength != null) ", Strength=$signalStrength" else ""
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Theme=${if (isLightTheme) "LIGHT" else "DARK"}, Color=${String.format("#%08X", iconColor)}$strengthLog")

        // Apply color filter for maximum contrast (consistent theming approach)
        drawable.setColorFilter(iconColor, android.graphics.PorterDuff.Mode.SRC_IN)
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Color filter applied")
        
        // Set bounds for icon positioning (consistent bounds calculation)
        val bounds = android.graphics.Rect(
            x.toInt(),
            (centerY - iconSize / 2).toInt(),
            (x + iconSize).toInt(),
            (centerY + iconSize / 2).toInt()
        )
        drawable.setBounds(bounds.left, bounds.top, bounds.right, bounds.bottom)
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Bounds set to ${bounds}")
        
        // Draw the icon to canvas
        drawable.draw(canvas)
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Icon drawn successfully at position ($x, $centerY) with size $iconSize")

        return iconSize.toFloat()
    }

    /**
     * Draws the composite battery+emoji indicator using loaded bitmap images
     * This matches the visual appearance from the customize screen's live preview
     */
    private fun drawBatteryIndicator(canvas: Canvas, x: Float, centerY: Float, status: CoreBatteryStatus, config: CustomizationConfig): Float {
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: ========== DRAWING COMPOSITE BATTERY+EMOJI ==========")
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Position: ($x, $centerY)")
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Battery bitmap available: ${batteryBitmap != null}")
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Emoji bitmap available: ${emojiBitmap != null}")
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Show emoji: ${config.customConfig.showEmoji}")
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Show percentage: ${config.customConfig.showPercentage}")

        // Calculate composite size based on configuration
        val scaleFactor = 1.5f
        val baseSize = defaultBatteryHeight.toFloat() * scaleFactor // Use battery height as base size
        val emojiScale = config.customConfig.emojiSizeScale
        val compositeWidth = baseSize * 1.2f // Allow some extra width for composite
        val compositeHeight = baseSize

        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Base size: $baseSize, Emoji scale: $emojiScale")
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Composite dimensions: ${compositeWidth}x$compositeHeight")
        BatteryLogger.d("ICON_SIZE_TEST", "Battery icon composite size: ${compositeWidth}x$compositeHeight")


        // Calculate positioning for composite element
        val compositeLeft = x
        val compositeTop = centerY - compositeHeight / 2f
        val compositeRight = compositeLeft + compositeWidth
        val compositeBottom = compositeTop + compositeHeight

        // Layer 1: Draw battery image (base layer)
        val currentBatteryBitmap = batteryBitmap
        if (currentBatteryBitmap != null) {
            BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Drawing battery image")
            val batteryRect = RectF(compositeLeft, compositeTop, compositeRight, compositeBottom)
            canvas.drawBitmap(currentBatteryBitmap, null, batteryRect, null)
        } else {
            BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Drawing fallback battery shape")
            // Fallback: Draw generic battery shape if image not available
            drawFallbackBattery(canvas, compositeLeft, compositeTop, compositeWidth, compositeHeight, status)
        }

        // Layer 2: Draw emoji image (overlay layer) - positioned to match live preview
        val currentEmojiBitmap = emojiBitmap
        if (config.customConfig.showEmoji && currentEmojiBitmap != null) {
            BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Drawing emoji image overlay")

            // Calculate emoji positioning to match live preview appearance
            // The emoji should be overlaid on the battery, typically centered or slightly offset
            val emojiSize = baseSize * emojiScale
            val emojiLeft = compositeLeft + (compositeWidth - emojiSize) / 2f
            val emojiTop = compositeTop + (compositeHeight - emojiSize) / 2f
            val emojiRight = emojiLeft + emojiSize
            val emojiBottom = emojiTop + emojiSize

            val emojiRect = RectF(emojiLeft, emojiTop, emojiRight, emojiBottom)
            canvas.drawBitmap(currentEmojiBitmap, null, emojiRect, null)

            BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Emoji drawn at: ($emojiLeft, $emojiTop) size: $emojiSize")
            BatteryLogger.d("ICON_SIZE_TEST", "Emoji bitmap icon size: $emojiSize")
        } else if (config.customConfig.showEmoji) {
            BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Drawing fallback emoji character")
            // Fallback: Draw emoji character if image not available
            drawFallbackEmoji(canvas, compositeLeft, compositeTop, compositeWidth, compositeHeight, config)
        }

        // Layer 3: Draw percentage text (positioned near the composite element)
        if (config.customConfig.showPercentage) {
            BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Drawing percentage text")
            drawPercentageText(canvas, compositeLeft, compositeTop, compositeWidth, compositeHeight, status, config)
        }

        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Composite battery+emoji drawn successfully")
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: ========== COMPOSITE DRAWING COMPLETED ==========")

        return compositeWidth + elementSpacing
    }

    /**
     * Draws fallback battery shape when battery image is not available
     */
    private fun drawFallbackBattery(canvas: Canvas, left: Float, top: Float, width: Float, height: Float, status: CoreBatteryStatus) {
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Drawing fallback battery shape")

        // Battery outline
        val batteryRect = RectF(left, top, left + width * 0.9f, top + height)
        canvas.drawRoundRect(batteryRect, 2f, 2f, batteryStrokePaint)

        // Battery fill based on level
        val fillWidth = (width * 0.9f) * (status.percentage / 100f)
        val batteryFillRect = RectF(left + 1, top + 1, left + fillWidth - 1, top + height - 1)
        canvas.drawRoundRect(batteryFillRect, 1f, 1f, batteryPaint)

        // Battery terminal
        val terminalWidth = width * 0.1f
        val terminalHeight = height * 0.4f
        val terminalTop = top + (height - terminalHeight) / 2f
        val batteryTerminalRect = RectF(left + width * 0.9f, terminalTop, left + width, terminalTop + terminalHeight)
        canvas.drawRect(batteryTerminalRect, batteryStrokePaint)
    }

    /**
     * Draws fallback emoji character when emoji image is not available
     */
    private fun drawFallbackEmoji(canvas: Canvas, left: Float, top: Float, width: Float, height: Float, config: CustomizationConfig) {
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Drawing fallback emoji character")

        val emojiText = getEmojiCharacter(config)
        val emojiSize = height * config.customConfig.emojiSizeScale * 0.8f // Slightly smaller for better fit

        val emojiPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            textSize = emojiSize
            textAlign = Paint.Align.CENTER
        }

        val emojiX = left + width / 2f
        val emojiY = top + height / 2f + emojiSize / 3f // Adjust for text baseline

        canvas.drawText(emojiText, emojiX, emojiY, emojiPaint)
    }

    /**
     * Draws percentage text positioned near the composite battery+emoji element
     */
    private fun drawPercentageText(canvas: Canvas, left: Float, top: Float, width: Float, height: Float, status: CoreBatteryStatus, config: CustomizationConfig) {
        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Drawing percentage text")

        val percentageText = "${status.percentage}%"
        val textBounds = Rect()
        textPaint.getTextBounds(percentageText, 0, percentageText.length, textBounds)

        // Position text to the right of the composite element (similar to live preview)
        val textX = left + width + 4f // Small spacing from composite element
        val textY = top + height / 2f + textBounds.height() / 2f

        canvas.drawText(percentageText, textX, textY, textPaint)

        BatteryLogger.d(EMOJI_DRAW_TAG, "COMPOSITE_DRAW_DEBUG: Percentage text '$percentageText' drawn at ($textX, $textY) with color ${String.format("#%08X", textPaint.color)}")
    }

    // Measurement helper methods
    private fun measureTimeWidth(): Float {
        val timePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            textSize = (12 * resources.displayMetrics.scaledDensity)  // Updated size
            typeface = Typeface.DEFAULT_BOLD
        }
        return timePaint.measureText("18:09")  // Sample time format
    }

    private fun measureSilentIconWidth(): Float {
        return (16 * resources.displayMetrics.density)  // Updated size for better visibility
    }

    private fun measureCellularIconWidth(): Float {
        return (16 * resources.displayMetrics.density)  // Updated size for better visibility
    }

    private fun measureWifiIconWidth(): Float {
        return (16 * resources.displayMetrics.density)  // Updated size for better visibility
    }

    private fun measurePercentageWidth(status: CoreBatteryStatus): Float {
        val percentageText = "${status.percentage}%"
        return textPaint.measureText(percentageText)
    }

    /**
     * Handles touch events for gesture detection
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        BatteryLogger.v(EMOJI_GESTURE_TAG, "Touch event: ${event.action}")
        
        // Use gesture detector for swipe gestures
        val gestureHandled = gestureDetector.onTouchEvent(event)
        
        if (gestureHandled) {
            BatteryLogger.d(EMOJI_GESTURE_TAG, "Gesture handled by detector")
            return true
        }
        
        // Allow touch events to pass through for other interactions
        return super.onTouchEvent(event)
    }
    
    /**
     * Cleans up emoji view resources
     */
    fun cleanup() {
        BatteryLogger.d(TAG, "Cleaning up EmojiBatteryView resources")
        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_VIEW_CLEANUP_STARTED")

        emojiBitmap?.recycle()
        batteryBitmap?.recycle()
        emojiBitmap = null
        batteryBitmap = null

        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_VIEW_CLEANUP_COMPLETED")
    }
}
